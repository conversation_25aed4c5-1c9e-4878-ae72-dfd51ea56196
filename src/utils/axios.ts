import axios, {AxiosInstance, AxiosRequestConfig} from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth' // get token from cookie
// 创建axios实例

declare module "axios" {
  interface AxiosResponse<T = any> {
    msg: string;
    code: string;
    title: string;
    // 这里追加你的参数 解决 TS2339: Property 'error' does not exist on type 'AxiosResponse '.
  }

  export function create(config?: AxiosRequestConfig): AxiosInstance;
}

const service = axios.create({
  withCredentials: false,
  // baseURL: 'https://xczx7.swufe.edu.cn/oc/xczk/',
   // baseURL: 'http://127.0.0.1:8000/',
    baseURL: 'http://127.0.0.1:8000/',
  // baseURL: 'https://apps.swufe-online.com/kjapi/api/',
  // baseURL: 'https://localhost:5218/api/',
  timeout: 24000000
})

service.interceptors.request.use(
  (config: any) => {
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    if (config.url.indexOf("upload_to_drive")==-1){
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  error => {
    Promise.reject(error)
  }
)

// respone拦截器
service.interceptors.response.use(
  (response: any) => {
    if (response.status !== 200) {
      ElMessage({
        message: response.msg,
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(response)
    } else {

      return response.data
    }
  },
  error => {
    if (error.response) {
      const res = { code: "", data: null, msg: "" }
      res.code = error.response.status
      res.data = error.response.data
      res.msg = throwErr(error.response.status, error.response)
      ElMessage({
        message: res.msg,
        type: 'error',
        duration: 5 * 1000
      })

      return Promise.reject(res)
    } else {
      return Promise.reject(error)
    }
  }
)

export const throwErr = (code: number, response: any) => {
  let message = '请求错误'
  switch (code) {
    case 400:
      message = '请求错误'
      break
    case 401:
      message = '未授权，请登录'
      break
    case 403:
      message = '拒绝访问'
      break
    case 404:
      message = `请求地址出错: ${response.config.url}`
      break
    case 408:
      message = '请求超时'
      break
    case 500:
      message = '服务器内部错误'
      break
    case 501:
      message = '服务未实现'
      break
    case 502:
      message = '网关错误'
      break
    case 503:
      message = '服务不可用'
      break
    case 504:
      message = '网关超时'
      break
    case 505:
      message = 'HTTP版本不受支持'
      break
    default:
  }
  return message
}

export default service
